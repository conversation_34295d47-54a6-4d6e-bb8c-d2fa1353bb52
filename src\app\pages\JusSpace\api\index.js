import axios from "axios";

const baseAPI = window.location.host === 'localhost:3000' ? "http://localhost:4444/api" : "https://api.juspage.jusfy.dev/api";

export const domainSuggestions = async (name) => {
  const response = await axios.get(`${baseAPI}/jusmail/domain-suggestions`, {
    params: {
      name
    }
  });
  return response.data;
};

export const checkDomainAvailability = async (domain) => {
  const response = await axios.get(`${baseAPI}/jusmail/check-availability`, {
    params: {
      domain
    }
  });
  return response.data;
};

export const registerDomain = async (domain, email) => {
  const response = await axios.post(`${baseAPI}/jusmail`, {
    domain,
    email
  });
  return response.data;
};
