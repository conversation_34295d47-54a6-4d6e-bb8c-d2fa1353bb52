import styled from "styled-components";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";

export const Container = styled.div`
  padding: 2rem;
  margin: 0 auto;
  width: 100%;
  max-width: 1200px;
`;

export const ContentContainer = styled.div`
  width: 100%;
`;

export const StepContainer = styled.div`
  display: flex;
  gap: 2rem;
  min-height: 500px;
  opacity: 0;
  transform: translateX(20px);
  animation: slideInFade 0.5s ease-out forwards;

  @keyframes slideInFade {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1.5rem;
  }
`;

export const FormColumn = styled.div`
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 2rem;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #E7E8EC;
  padding: 2rem;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out 0.1s forwards;

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    flex: 1;
  }
`;

export const ContentColumn = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F8F9FA;
  border-radius: 10px;
  border: 1px solid #E7E8EC;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out 0.2s forwards;

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    min-height: 200px;
  }
`;

export const FormSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.7s ease-out 0.3s forwards;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

export const Title = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  color: #3F4254;
  margin: 0;
`;

export const ChipsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

export const DomainChip = styled(Chip)`
  &.MuiChip-root {
    background-color: ${props => props.selected ? '#E6F7F2' : '#F4F5F9'};
    color: ${props => props.selected ? '#01AB7D' : '#7E8299'};
    border: 1px solid ${props => props.selected ? '#01AB7D' : '#E7E8EC'};
    font-weight: 700;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: ${props => props.selected ? '#E6F7F2' : '#E7E8EC'};
    }

    .MuiChip-label {
      padding: 12px;
    }
  }
`;

export const Separator = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #E7E8EC;
  }

  span {
    color: #7E8299;
    font-size: 0.875rem;
    white-space: nowrap;
  }
`;

export const CustomTextField = styled(TextField)`
  &.MuiTextField-root {
    .MuiOutlinedInput-root {
      border-radius: 8px;

      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: #01AB7D;
      }

      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: #01AB7D;
      }
    }

    .MuiInputLabel-root {
      &.Mui-focused {
        color: #01AB7D;
      }
    }
  }
`;

export const ProgressContainer = styled.div`
  margin-bottom: 1.5rem;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.6s ease-out 0.4s forwards;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

export const ProgressLabel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #7E8299;
`;

export const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background-color: #E7E8EC;
  border-radius: 3px;
  overflow: hidden;
`;

export const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #01AB7D 0%, #00D4AA 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

export const ActionsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.6s ease-out 0.5s forwards;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

export const ActionButton = styled(Button)`
  &.MuiButton-root {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: none;
    min-width: 120px;

    &.primary {
      background-color: #01AB7D;
      color: white;

      &:hover {
        background-color: #019973;
      }

      &:disabled {
        background-color: #E7E8EC;
        color: #7E8299;
      }
    }

    &.secondary {
      background-color: transparent;
      color: #7E8299;
      border: 1px solid #E7E8EC;

      &:hover {
        background-color: #F4F5F9;
        border-color: #7E8299;
      }
    }
  }
`;

export const PlaceholderContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #7E8299;
  text-align: center;
  padding: 2rem;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
`;

export const SvgContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2rem;
  opacity: 0;
  transform: scale(0.95);
  animation: scaleInFade 0.8s ease-out 0.3s forwards;

  @keyframes scaleInFade {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

export const SvgWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 503px;
`;

export const DomainOverlay = styled.div`
  position: absolute;
  top: 4%;
  left: 21%;
  right: 21%;
  height: 6%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  z-index: 10;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }

  @media (max-width: 480px) {
    font-size: 0.625rem;
  }
`;

export const EmailOverlay = styled.div`
  position: absolute;
  top: 30%;
  left: 21%;
  width: 100%
  height: 8%;
  display: flex;
  align-items: center;
  justify-content: start;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  z-index: 10;
  padding: 8px 12px;
  background-color: #01AB7D;
  border-radius: 50px;

  @media (max-width: 768px) {
    font-size: 0.875rem;
  }

  @media (max-width: 480px) {
    font-size: 0.75rem;
  }
`;

export const PasswordOverlay = styled.div`
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #3F4254;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  z-index: 10;
  letter-spacing: 2px;
  border-radius: 25px;
  border: 1px solid #01AB7D;
  padding: 8px 12px;
  min-width: 175px;
  max-width: 275px;

  @media (max-width: 768px) {
    font-size: 1rem;
  }

  @media (max-width: 480px) {
    font-size: 0.875rem;
  }
`;

export const StrengthOverlay = styled.div`
  display: flex;
  flex-direction: column;
  align-items: start;
  color: ${props => {
    const colors = ["#FF4444", "#FF8800", "#FFAA00", "#88CC00", "#00AA44", "#01AB7D"];
    return colors[props.strengthLevel] || "#FF4444";
  }};
  width: 175px;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  z-index: 10;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }

  @media (max-width: 480px) {
    font-size: 0.625rem;
  }
`;
