import React, { useState } from "react";
import * as S from "../styles";

const Step2 = ({ formData, onFormDataChange, onNext, onBack }) => {
  const [professionalEmail, setProfessionalEmail] = useState(formData.professionalEmail || "");

  const selectedDomain = formData.selectedDomain || formData.customDomain;

  const handleEmailChange = (event) => {
    const value = event.target.value.toLowerCase().replace(/[^a-z0-9._-]/g, '');
    setProfessionalEmail(value);
    onFormDataChange("professionalEmail", value);
  };

  const handleNext = () => {
    if (professionalEmail.trim()) {
      onNext();
    }
  };

  const handleBack = () => {
    onBack();
  };

  const isNextDisabled = !professionalEmail.trim();

  return (
    <S.StepContainer>
      <S.FormColumn>
        <S.ProgressContainer>
          {/* <S.ProgressLabel>
            <span>Progresso</span>
            <span>Etapa 2 de 3</span>
          </S.ProgressLabel> */}
          <S.ProgressBar>
            <S.ProgressFill progress={66.66} />
          </S.ProgressBar>
        </S.ProgressContainer>

        <S.FormSection>
          <div>
            <S.Title>E-mail profissional</S.Title>
            <S.Title style={{ color: "#01AB7D" }}>
              O contato que seus clientes vão lembrar.
            </S.Title>
          </div>

          <div>
            <h4 style={{ margin: "0 0 1rem 0", color: "#3F4254", fontSize: "1.1rem" }}>
              Criar e-mail profissional
            </h4>
            <S.CustomTextField
              fullWidth
              label="Digite o nome do seu e-mail"
              placeholder="contato"
              value={professionalEmail}
              onChange={handleEmailChange}
              InputProps={{
                endAdornment: <span style={{ color: "#7E8299" }}>@{selectedDomain}.com.br</span>
              }}
              helperText="Apenas letras, números, pontos, hífens e underscores são permitidos"
            />

            {professionalEmail && (
              <div style={{
                marginTop: "1rem",
                padding: "1rem",
                backgroundColor: "#F8F9FA",
                borderRadius: "8px",
                border: "1px solid #E7E8EC"
              }}>
                <div style={{ color: "#7E8299", fontSize: "0.875rem", marginBottom: "0.5rem" }}>
                  Seu e-mail profissional será:
                </div>
                <strong style={{ color: "#01AB7D", fontSize: "1rem" }}>
                  {professionalEmail}@{selectedDomain}.com.br
                </strong>
              </div>
            )}
          </div>
        </S.FormSection>

        <S.ActionsContainer>
          <S.ActionButton
            variant="outlined"
            className="secondary"
            onClick={handleBack}
          >
            Voltar
          </S.ActionButton>
          <S.ActionButton
            variant="contained"
            className="primary"
            onClick={handleNext}
            disabled={isNextDisabled}
          >
            Próximo
          </S.ActionButton>
        </S.ActionsContainer>
      </S.FormColumn>

      <S.ContentColumn>
        <S.SvgContainer>
          <S.SvgWrapper>
            <img
              src="/media/jusspace/email-form.svg"
              alt="Email form mockup"
              style={{ width: "100%", height: "auto", maxWidth: "600px" }}
            />
            <span style={{ position: "absolute", top: "5%", left: "15%", right: "15%", color: "#F8F9FA", fontSize: "1rem", fontWeight: "600", textAlign: "center" }}>
              {selectedDomain}.com.br
            </span>
            {professionalEmail && (
              <S.EmailOverlay>
                {professionalEmail}@{selectedDomain}.com.br
              </S.EmailOverlay>
            )}
          </S.SvgWrapper>
        </S.SvgContainer>
      </S.ContentColumn>
    </S.StepContainer>
  );
};

export default Step2;
