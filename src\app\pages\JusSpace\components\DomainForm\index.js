import React, { useState } from "react";
import Step1 from "./Steps/Step1";
import Step2 from "./Steps/Step2";
import Step3 from "./Steps/Step3";
import * as S from "./styles";

const steps = ["Domínio", "E-mail", "Finalização"];

export const DomainForm = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    selectedDomain: "",
    customDomain: "",
    professionalEmail: "",
    // Add more form fields as needed for other steps
  });

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFormDataChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Step1
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onNext={handleNext}
            onBack={handleBack}
            isFirstStep={true}
          />
        );
      case 1:
        return (
          <Step2
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 2:
        return (
          <Step3
            formData={formData}
            onFormDataChange={handleFormDataChange}
            onNext={handleNext}
            onBack={handleBack}
            isLastStep={true}
          />
        );
      default:
        return null;
    }
  };

  return (
    <S.Container>
      <S.ContentContainer>
        {renderStepContent(activeStep)}
      </S.ContentContainer>
    </S.Container>
  );
};
