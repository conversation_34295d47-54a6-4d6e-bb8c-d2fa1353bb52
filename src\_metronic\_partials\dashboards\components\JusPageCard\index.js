import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import * as S from "./JusPageCard.styles";
import { ArrowForward } from "./icons/ArrowForward";
import { BrowserCheck } from "./icons/BrowserCheck";
import {
  getSiteConfigExperimental,
  getStatistics,
} from '../../../../../app/pages/JusPage/api';
import {
  EventsSegment
} from '../../../../../app/pages/JusPage/helpers/EventsSegmentJusPage';
const { EventDashboardSegment } = EventsSegment();

const JusPageCard = () => {
  const authToken = useSelector((state) => state.auth.authToken);
  const [siteState, setSiteState] = useState('WAITING');
  // const [availableSlots, setAvailableSlots] = useState(0);
  const [siteUrl, setSiteUrl] = useState('');

  useEffect(() => {
      fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const responseStatistics = await getStatistics(authToken);

      if (responseStatistics?.data?.userHasConfig === false) {
        setSiteState('NOT_CREATED');
        EventDashboardSegment("Juspage Banner Viewed");
      }else{
        setSiteState(responseStatistics.data?.is_published === true ? 'FINISHED' : 'IN_PROGRESS');
        setSiteUrl(responseConfigs.data.domain);
      }
    } catch (error) {
      console.error("🚀 ~ fetchData ~ error:", error)
    }
  };

  const handleClick = (event) => {
    EventDashboardSegment("Juspage Banner Clicked");
    window.open(window.location.host === 'localhost:3000' ? 'http://localhost:3000/page' : `/page`, '_self');
  };
  
  const handleClickDomain = () => {
    EventDashboardSegment("Juspage View Button Clicked");
    window.open(window.location.host === 'localhost:3000' ? 'http://localhost:3030' : `https://${siteUrl}`, '_blank');
  };

  const renderFirstStepComponent = () => (
    <>
    {/* <pre>{siteState}</pre> */}
      { siteState === 'NOT_CREATED' &&
      <S.JusPageCard>
        <S.BadgeContainer 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          style={
            {
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: 'var(--xx-small-4-px, 4px) var(--small-12-px, 12px)',
              borderRadius: 'var(--large, 7px)',
              background: 'var(--secondary-100, #FDEDE7)',
            }
          }
        >
          <ArrowForward />
          <S.BadgeText>Novidade!</S.BadgeText>
        </S.BadgeContainer>
        <S.BadgeContainerSlots 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
        </S.BadgeContainerSlots>
        <S.TitleJusPageCard>Crie seu site em 5 minutos</S.TitleJusPageCard>
        <S.DescriptionJusPageCard>Responda algumas perguntas para a Ju criar um site especialmente para você. Sem complicações e de graça!</S.DescriptionJusPageCard>
        <S.CardButton onClick={() => handleClick("Juspage Banner Clicked")}>Criar meu site gratuito</S.CardButton>
        <S.JuImage />
      </S.JusPageCard>
      }
      { siteState === 'FINISHED' && <S.JusPageCardFinished>
        <S.TitleWraper>
          <S.TitleJusPageCard>Meu site</S.TitleJusPageCard>
          <S.EditButton onClick={() => handleClick("Juspage Edit Button Clicked")}>Editar</S.EditButton>
        </S.TitleWraper>
        <S.OnlineWrapper>
          <BrowserCheck stroke="#01AB7D" />
          Seu site está no ar!
          <S.OnlineButton onClick={() => handleClickDomain()} >Acessar</S.OnlineButton>
        </S.OnlineWrapper>
      </S.JusPageCardFinished>
      }
      { siteState === 'IN_PROGRESS' && <S.JusPageCardFinished>
        <S.TitleJusPageCard>Meu site</S.TitleJusPageCard>
        <S.InProgressWrapper>
          <BrowserCheck stroke="#CC9F00" />
          Você tem alterações não salvas
        </S.InProgressWrapper>
        <S.InProgressButton onClick={() => handleClick("Juspage Edit and Publish Button Clicked")} >Editar e publicar</S.InProgressButton>
      </S.JusPageCardFinished>
      }
    </>
  );

  return <>{renderFirstStepComponent()} </>;
};

export default JusPageCard;
