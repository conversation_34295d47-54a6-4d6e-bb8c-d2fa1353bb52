import { useState, useEffect } from "react";
import { EventsSegment } from "../../helpers/EventsSegmentsCalculators";
import * as S from "./styles";
import LanguageIcon from '@mui/icons-material/Language';
import ImageSearchOutlinedIcon from '@mui/icons-material/ImageSearchOutlined';
import AutoAwesomeOutlinedIcon from '@mui/icons-material/AutoAwesomeOutlined';
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
import PublicIcon from '@mui/icons-material/Public';
import MailIcon from '@mui/icons-material/Mail';
import InsightsIcon from '@mui/icons-material/Insights';
import GoogleIcon from '@mui/icons-material/Google';
import AppRegistrationIcon from '@mui/icons-material/AppRegistration';
import ImportantDevicesOutlinedIcon from '@mui/icons-material/ImportantDevicesOutlined';
import WebAssetOutlinedIcon from '@mui/icons-material/WebAssetOutlined';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import { getStatistics } from "../JusPage/api";
import { useSelector } from "react-redux";
import { Tooltip } from "@mui/material";

export const JusSpace = () => {
  const authToken = useSelector((state) => state.auth.authToken);
  const { HandleEvent } = EventsSegment();
  const [statistics, setStatistics] = useState(true);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        if (authToken) {
          const { data } = await getStatistics(authToken);
          setStatistics(data);
        }
      } catch (err) {
        console.error("Error fetching statistics:", err);
      }
    };

    fetchStatistics();
  }, []);

  const handleClick = (event) => {
    EventDashboardSegment(event);
    window.open(window.location.host === 'localhost:3000' ? 'http://localhost:3000/page' : `/page`, '_self');
  };

  const handleClickDomain = () => {
    EventDashboardSegment("Juspage View Button Clicked");
    window.open(window.location.host === 'localhost:3000' ? 'http://localhost:3030' : `https://${statistics.domain}`, '_blank');
  };

  const handleCreateDomain = () => {
    HandleEvent("Create Domain Button Clicked");
    window.open(window.location.host === 'localhost:3000' ? 'http://localhost:3000/space/domain' : `/space/domain`, '_self');
  };

  return (
    <S.Container>
      <S.HeaderSection>
        <S.Title>Com <span style={{ color: "#01AB7D" }}>JusSpace</span>, sua advocacia conquista território digital: tudo em um só lugar.</S.Title>
        <S.Subtitle>Crie seu site profissional, registra um domínio exclusivo e e-mails personalizados com segurança. Tudo integrado, rápido e feito para advogados.</S.Subtitle>
      </S.HeaderSection>

      <S.CardsContainer>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', backgroundColor: "#fff", borderRadius: '10px', padding: '2rem', border: '1px solid #E7E8EC' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <S.IconSite>
                <PublicIcon />
              </S.IconSite>
              <div style={{ display: 'flex', flexDirection: 'column'}}>
                <span style={{ color: '#7E8299' }}>JusPage</span>
                <S.CardTitle>Criador de Sites</S.CardTitle>
              </div>
            </div>
            <span style={{ color: '#01AB7D', backgroundColor: '#E6F7F2', padding: '4px 8px', borderRadius: '8px' }}>
              Incluso no seu plano atual
            </span>
          </div>
          {!statistics.userHasConfig || !statistics.is_published ?
            (<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', backgroundColor: !statistics.userHasConfig ? '#F4F5F9' : 'var(--warning-100, #FFF9E6)', borderRadius: '10px', padding: '12px' }}>
              <div style={{display: 'flex', alignItems: 'center', gap: '12px' }}>
                {!statistics.userHasConfig
                  ? <WebAssetOutlinedIcon sx={{color: '#01AB7D !important'}} />
                  : !statistics.is_published && <WarningIcon sx={{color: '#CC9F00 !important'}} />
                }
                <div>
                  <h4 style={{margin: 0, fontSize: '1.2rem', fontWeight: 'semibold'}}>{!statistics.userHasConfig ? 'Crie seu site agora mesmo' : 'Você tem alterações não salvas'}</h4>
                </div>
              </div>
              <button
                onClick={() => HandleEvent("Create JusPage Site Clicked")}
                style={{
                  backgroundColor: "#01AB7D",
                  color: "#FFFFFF",
                  border: "none",
                  borderRadius: "10px",
                  padding: "10px 24px",
                  textTransform: "none",
                  fontWeight: 600,
                  cursor: "pointer",
                  boxShadow: "none",
                }}
                onMouseOver={e => e.currentTarget.style.backgroundColor = "#019973"}
                onMouseOut={e => e.currentTarget.style.backgroundColor = "#01AB7D"}
              >
                {!statistics.userHasConfig ? 'Criar' : 'Ver'}
              </button>
            </div>) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', backgroundColor: "#fff", borderRadius: '10px', padding: '1rem', border: '1px solid #E7E8EC' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <h4 style={{margin: 0, fontSize: '1.2rem', fontWeight: 'semibold'}}>Meu site</h4>
                  <button
                    onClick={() => handleClick("Juspage Edit Button Clicked")}
                    style={{
                      color: "#01AB7D",
                      backgroundColor: 'inherit',
                      border: "none",
                      borderRadius: "10px",
                      padding: "10px 24px",
                      textTransform: "none",
                      fontWeight: 600,
                      cursor: "pointer",
                      boxShadow: "none",
                    }}
                    >
                      Editar
                    </button>
                </div>
                <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', backgroundColor: '#F4F5F9', borderRadius: '10px', padding: '12px' }}>
                  <div style={{display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <WebAssetOutlinedIcon sx={{color: '#01AB7D !important'}} />
                    <h4 style={{margin: 0, fontSize: '1.2rem', fontWeight: 'semibold'}}>Seu site está no ar</h4>
                  </div>
                  <button
                    onClick={handleClickDomain}
                    style={{
                      backgroundColor: "#01AB7D",
                      color: "#FFFFFF",
                      border: "none",
                      borderRadius: "10px",
                      padding: "10px 24px",
                      textTransform: "none",
                      fontWeight: 600,
                      cursor: "pointer",
                      boxShadow: "none",
                    }}
                    onMouseOver={e => e.currentTarget.style.backgroundColor = "#019973"}
                    onMouseOut={e => e.currentTarget.style.backgroundColor = "#01AB7D"}
                    >
                      Acessar
                    </button>
                </div>
              </div>
            )}
          <S.FeatureList>
            <S.ListSite>
              <S.IconSite>
                <LanguageIcon />
              </S.IconSite>
              <span>Criação e Customização de sites</span>
            </S.ListSite>
            <S.ListSite>
              <S.IconSite>
                <ImageSearchOutlinedIcon />
              </S.IconSite>
              <span>Banco de imagens para customização</span>
            </S.ListSite>
            <S.ListSite>
              <S.IconSite>
                <AutoAwesomeOutlinedIcon />
              </S.IconSite>
              <span>Geração do logos com IA</span>
            </S.ListSite>
          </S.FeatureList>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', backgroundColor: "#fff", borderRadius: '10px', padding: '2rem', border: '1px solid #E7E8EC' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <S.IconInfo>
                <ImportantDevicesOutlinedIcon sx={{color: '#E94F0E !important'}} />
              </S.IconInfo>
              <div style={{ display: 'flex', flexDirection: 'column'}}>
                <span style={{ color: '#7E8299' }}>JusMail</span>
                <S.CardTitle style={{ display: 'flex', alignItems: 'center' }}>
                  Seu nome
                  <span style={{ color: "#E94F0E" }}>.com.br</span>
                  <Tooltip className="ml-2" title="O domínio .com.br é um dos mais populares e reconhecidos no Brasil. Ele é associado a confiabilidade e credibilidade, tornando-o uma escolha popular para negócios e organizações. Além disso, o .com.br é amplamente reconhecido e facilmente memorizado, o que pode ajudar a lembrar o endereço do seu e-mail ou site.">
                    <InfoIcon sx={{color: '#7E8299 !important'}} />
                  </Tooltip>
                </S.CardTitle>

              </div>
            </div>
            <div>
              <S.Price>R$ 9,90</S.Price>
              <S.PriceUnit>/mês*</S.PriceUnit>
            </div>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', backgroundColor: '#F4F5F9', borderRadius: '10px', padding: '12px' }}>
            <div style={{display: 'flex', alignItems: 'center', gap: '12px' }}>
              <AppRegistrationIcon sx={{color: '#E94F0E !important'}} />
              <h4 style={{margin: 0, fontSize: '1.2rem', fontWeight: 'semibold'}}>Seu escritório na web</h4>
            </div>
            <button
              onClick={handleCreateDomain}
              style={{
                backgroundColor: "#E94F0E",
                color: "#FFFFFF",
                border: "none",
                borderRadius: "10px",
                padding: "10px 24px",
                textTransform: "none",
                fontWeight: 600,
                cursor: "pointer",
                boxShadow: "none",
              }}
              onMouseOver={e => e.currentTarget.style.backgroundColor = "#D04000"}
              onMouseOut={e => e.currentTarget.style.backgroundColor = "#E94F0E"}
            >
              Criar
            </button>
          </div>
          <S.FeatureList>
            <S.ListInfo>
              <S.IconInfo>
                <TravelExploreIcon sx={{color: '#E94F0E !important'}} />
              </S.IconInfo>
              <span>Dominio personalizado*</span>
            </S.ListInfo>
            <S.ListInfo>
              <S.IconInfo>
                <MailIcon sx={{color: '#E94F0E !important'}} />
              </S.IconInfo>
              <span>E-mail profissional*</span>
            </S.ListInfo>
            <S.ListInfo>
              <S.IconInfo>
                <InsightsIcon sx={{color: '#E94F0E !important'}} />
              </S.IconInfo>
              <span>Dashboard personalizada</span>
            </S.ListInfo>
            <S.ListInfo>
              <S.IconInfo>
                <GoogleIcon sx={{color: '#E94F0E !important'}} />
              </S.IconInfo>
              <span>Recursos de SEO </span>
            </S.ListInfo>
          </S.FeatureList>
        </div>
      </S.CardsContainer>
    </S.Container>
  );
};