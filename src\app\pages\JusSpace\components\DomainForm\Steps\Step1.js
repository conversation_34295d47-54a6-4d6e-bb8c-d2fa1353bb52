import React, { useState } from "react";
import * as S from "../styles";

const predefinedDomains = [
  "advocacia",
  "juridico",
  "direito",
  "advogados",
  "escritorio"
];

const Step1 = ({ formData, onFormDataChange, onNext, isFirstStep }) => {
  const [selectedDomain, setSelectedDomain] = useState(formData.selectedDomain || "");
  const [customDomain, setCustomDomain] = useState(formData.customDomain || "");

  const handleDomainSelect = (domain) => {
    setSelectedDomain(domain);
    setCustomDomain(""); // Clear custom domain when selecting predefined
    onFormDataChange("selectedDomain", domain);
    onFormDataChange("customDomain", "");
  };

  const handleCustomDomainChange = (event) => {
    const value = event.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setCustomDomain(value);
    setSelectedDomain(""); // Clear selected domain when typing custom
    onFormDataChange("customDomain", value);
    onFormDataChange("selectedDomain", "");
  };

  const handleNext = () => {
    if (selectedDomain || customDomain.trim()) {
      onNext();
    }
  };

  const isNextDisabled = !selectedDomain && !customDomain.trim();

  return (
    <S.StepContainer>
      <S.FormColumn>
        <S.FormSection>
          <div>
            <S.Title>Tudo começa com um nome.</S.Title>
            <S.Title style={{ color: "#01AB7D" }}>
               Escolha o seu domínio.
            </S.Title>
          </div>

          <div>
            {/* <h4 style={{ margin: "0 0 1rem 0", color: "#3F4254", fontSize: "1.1rem" }}>
              Domínios sugeridos
            </h4> */}
            <S.ChipsContainer>
              {predefinedDomains.map((domain) => (
                <S.DomainChip
                  key={domain}
                  label={`${domain}.com.br`}
                  selected={selectedDomain === domain}
                  onClick={() => handleDomainSelect(domain)}
                />
              ))}
            </S.ChipsContainer>
          </div>

          <S.Separator>
            <span>ou</span>
          </S.Separator>

          <div>
            <h4 style={{ margin: "0 0 1rem 0", color: "#3F4254", fontSize: "1.1rem" }}>
              Domínio personalizado
            </h4>
            <S.CustomTextField
              fullWidth
              label="Digite seu domínio personalizado"
              placeholder="meuescritorio"
              value={customDomain}
              onChange={handleCustomDomainChange}
              InputProps={{
                endAdornment: <span style={{ color: "#7E8299" }}>.com.br</span>
              }}
              helperText="Apenas letras, números e hífens são permitidos"
            />
          </div>
        </S.FormSection>

        <S.ActionsContainer>
          <div>
            {/* Empty div for spacing since this is the first step */}
          </div>
          <S.ActionButton
            variant="contained"
            className="primary"
            onClick={handleNext}
            disabled={isNextDisabled}
          >
            Próximo
          </S.ActionButton>
        </S.ActionsContainer>
      </S.FormColumn>

      <S.ContentColumn>
        <S.SvgContainer>
          <S.SvgWrapper>
            <img
              src="/media/jusspace/domain-form.svg"
              alt="Browser mockup"
              style={{ width: "100%", height: "auto", maxWidth: "503px" }}
            />
            {(selectedDomain || customDomain) && (
              <S.DomainOverlay>
                {selectedDomain || customDomain}.com.br
              </S.DomainOverlay>
            )}
          </S.SvgWrapper>
        </S.SvgContainer>
      </S.ContentColumn>
    </S.StepContainer>
  );
};

export default Step1;
