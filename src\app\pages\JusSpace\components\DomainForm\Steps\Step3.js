import React from "react";
import * as S from "../styles";

const Step3 = ({ formData, onFormDataChange, onNext, onBack, isLastStep }) => {
  const handleFinish = () => {
    // Handle form submission here
    console.log("Form submitted with data:", formData);
    // You can add API calls or other logic here
  };

  const handleBack = () => {
    onBack();
  };

  const selectedDomain = formData.selectedDomain || formData.customDomain;

  return (
    <S.StepContainer>
      <S.FormColumn>
        <S.FormSection>
          <div>
            <S.Title>Finalização</S.Title>
            <S.Title style={{ color: "#01AB7D" }}>
              Revise suas informações e finalize a criação do seu domínio
            </S.Title>
          </div>

          <div style={{
            padding: "1.5rem",
            backgroundColor: "#F8F9FA",
            borderRadius: "8px",
            border: "1px solid #E7E8EC"
          }}>
            <h4 style={{ margin: "0 0 1rem 0", color: "#3F4254" }}>
              Resu<PERSON> da configuração
            </h4>

            <div style={{ display: "flex", flexDirection: "column", gap: "0.75rem" }}>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <span style={{ color: "#7E8299" }}>Domínio selecionado:</span>
                <strong style={{ color: "#01AB7D" }}>
                  {selectedDomain ? `${selectedDomain}.com.br` : "Nenhum domínio selecionado"}
                </strong>
              </div>

              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <span style={{ color: "#7E8299" }}>Tipo:</span>
                <span style={{ color: "#3F4254" }}>
                  {formData.selectedDomain ? "Domínio sugerido" : "Domínio personalizado"}
                </span>
              </div>

              {formData.professionalEmail && (
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                  <span style={{ color: "#7E8299" }}>E-mail profissional:</span>
                  <strong style={{ color: "#01AB7D" }}>
                    {formData.professionalEmail}@{selectedDomain}.com.br
                  </strong>
                </div>
              )}
            </div>
          </div>

          <div style={{
            padding: "2rem",
            backgroundColor: "#F8F9FA",
            borderRadius: "8px",
            border: "1px solid #E7E8EC",
            textAlign: "center"
          }}>
            <h4 style={{ margin: "0 0 0.5rem 0", color: "#7E8299" }}>
              Próximos passos
            </h4>
            <p style={{ margin: "0", color: "#7E8299", fontSize: "0.875rem" }}>
              Após a finalização, você receberá instruções por email
            </p>
          </div>
        </S.FormSection>

        <S.ActionsContainer>
          <S.ActionButton
            variant="outlined"
            className="secondary"
            onClick={handleBack}
          >
            Voltar
          </S.ActionButton>
          <S.ActionButton
            variant="contained"
            className="primary"
            onClick={handleFinish}
            disabled={!selectedDomain}
          >
            Finalizar
          </S.ActionButton>
        </S.ActionsContainer>
      </S.FormColumn>

      <S.ContentColumn>
        <S.SvgContainer>
          <S.SvgWrapper>
            <img
              src="/media/jusspace/domain-form.svg"
              alt="Browser mockup"
              style={{ width: "100%", height: "auto", maxWidth: "503px" }}
            />
            {selectedDomain && (
              <S.DomainOverlay>
                https://{selectedDomain}.com.br
              </S.DomainOverlay>
            )}
          </S.SvgWrapper>
          {selectedDomain && (
            <div style={{
              marginTop: "1rem",
              padding: "1rem",
              backgroundColor: "white",
              borderRadius: "8px",
              border: "1px solid #01AB7D",
              textAlign: "center",
              maxWidth: "400px"
            }}>
              <div style={{ color: "#01AB7D", fontSize: "0.9rem", fontWeight: "600", marginBottom: "0.5rem" }}>
                ✓ Domínio confirmado
              </div>
              <div style={{ color: "#3F4254", fontSize: "1rem", fontWeight: "700" }}>
                {selectedDomain}.com.br
              </div>
            </div>
          )}
        </S.SvgContainer>
      </S.ContentColumn>
    </S.StepContainer>
  );
};

export default Step3;
