import React, { useState, useEffect } from "react";
import { Icon<PERSON>utton, InputAdornment } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import * as S from "../styles";

const Step3 = ({ formData, onFormDataChange, onNext, onBack, isLastStep }) => {
  const [password, setPassword] = useState(formData.password || "");
  const [confirmPassword, setConfirmPassword] = useState(formData.confirmPassword || "");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ level: 0, text: "Muito fraca" });

  // Password strength calculation
  const calculatePasswordStrength = (password) => {
    let score = 0;
    let feedback = [];

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push("Mínimo 8 caracteres");
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push("Letra minúscula");
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push("Letra maiúscula");
    }

    if (/[0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push("Número");
    }

    if (/[^A-Za-z0-9]/.test(password)) {
      score += 1;
    } else {
      feedback.push("Caractere especial");
    }

    const levels = [
      { level: 0, text: "Muito fraca", color: "#FF4444" },
      { level: 1, text: "Fraca", color: "#FF8800" },
      { level: 2, text: "Regular", color: "#FFAA00" },
      { level: 3, text: "Boa", color: "#88CC00" },
      { level: 4, text: "Forte", color: "#00AA44" },
      { level: 5, text: "Muito forte", color: "#01AB7D" }
    ];

    return {
      ...levels[score],
      feedback: feedback.slice(0, 3) // Show max 3 suggestions
    };
  };

  useEffect(() => {
    if (password) {
      setPasswordStrength(calculatePasswordStrength(password));
    } else {
      setPasswordStrength({ level: 0, text: "Muito fraca", color: "#FF4444", feedback: [] });
    }
  }, [password]);

  const handlePasswordChange = (e) => {
    const value = e.target.value;
    setPassword(value);
    onFormDataChange({ ...formData, password: value });
  };

  const handleConfirmPasswordChange = (e) => {
    const value = e.target.value;
    setConfirmPassword(value);
    onFormDataChange({ ...formData, confirmPassword: value });
  };

  const handleFinish = () => {
    if (password && confirmPassword && password === confirmPassword && passwordStrength.level >= 3) {
      console.log("Form submitted with data:", formData);
      // You can add API calls or other logic here
    }
  };

  const handleBack = () => {
    onBack();
  };

  const selectedDomain = formData.selectedDomain || formData.customDomain;
  const passwordsMatch = password && confirmPassword && password === confirmPassword;
  const isFormValid = password && confirmPassword && passwordsMatch && passwordStrength.level >= 3;

  return (
    <S.StepContainer>
      <S.FormColumn>
        <S.ProgressContainer>
          {/* <S.ProgressLabel>
            <span>Progresso</span>
            <span>Etapa 3 de 3</span>
          </S.ProgressLabel> */}
          <S.ProgressBar>
            <S.ProgressFill progress={100} />
          </S.ProgressBar>
        </S.ProgressContainer>

        <S.FormSection>
          <div>
            <S.Title>Mais segurança para sua conta.</S.Title>
            <S.Title style={{ color: "#01AB7D" }}>
              Configuração de Senha
            </S.Title>
          </div>

          <div>
            <h4 style={{ margin: "0 0 1rem 0", color: "#3F4254", fontSize: "1.1rem" }}>
              Seu email profissional
            </h4>
            <div style={{
              padding: "0.75rem 1rem",
              backgroundColor: "#E6F7F2",
              borderRadius: "8px",
              border: "1px solid #01AB7D",
              marginBottom: "1.5rem"
            }}>
              <strong style={{ color: "#01AB7D", fontSize: "1rem" }}>
                {formData.professionalEmail}@{selectedDomain}.com.br
              </strong>
            </div>
          </div>

          <S.CustomTextField
            fullWidth
            label="Senha"
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={handlePasswordChange}
            variant="outlined"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <S.CustomTextField
            fullWidth
            label="Confirmar Senha"
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            variant="outlined"
            error={confirmPassword && !passwordsMatch}
            helperText={confirmPassword && !passwordsMatch ? "As senhas não coincidem" : ""}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {password && (
            <div style={{
              padding: "1rem",
              backgroundColor: "#F8F9FA",
              borderRadius: "8px",
              border: "1px solid #E7E8EC"
            }}>
              <div style={{ display: "flex", alignItems: "center", gap: "0.5rem", marginBottom: "0.5rem" }}>
                <span style={{ color: "#7E8299", fontSize: "0.875rem" }}>Força da senha:</span>
                <span style={{
                  color: passwordStrength.color,
                  fontWeight: "600",
                  fontSize: "0.875rem"
                }}>
                  {passwordStrength.text}
                </span>
              </div>

              <div style={{
                width: "100%",
                height: "4px",
                backgroundColor: "#E7E8EC",
                borderRadius: "2px",
                overflow: "hidden",
                marginBottom: "0.5rem"
              }}>
                <div style={{
                  width: `${(passwordStrength.level / 5) * 100}%`,
                  height: "100%",
                  backgroundColor: passwordStrength.color,
                  transition: "all 0.3s ease"
                }} />
              </div>

              {passwordStrength.feedback && passwordStrength.feedback.length > 0 && (
                <div style={{ fontSize: "0.75rem", color: "#7E8299" }}>
                  Adicione: {passwordStrength.feedback.join(", ")}
                </div>
              )}
            </div>
          )}
        </S.FormSection>

        <S.ActionsContainer>
          <S.ActionButton
            variant="outlined"
            className="secondary"
            onClick={handleBack}
          >
            Voltar
          </S.ActionButton>
          <S.ActionButton
            variant="contained"
            className="primary"
            onClick={handleFinish}
            disabled={!isFormValid}
          >
            Finalizar
          </S.ActionButton>
        </S.ActionsContainer>
      </S.FormColumn>

      <S.ContentColumn>
        <S.SvgContainer>
          <S.SvgWrapper>
            <img
              src="/media/jusspace/pass-form.svg"
              alt="Password form mockup"
              style={{ width: "100%", height: "auto", maxWidth: "503px" }}
            />
            <span style={{ position: "absolute", top: "5%", left: "15%", right: "15%", color: "#F8F9FA", fontSize: "1rem", fontWeight: "600", textAlign: "center" }}>
              {selectedDomain}.com.br
            </span>
            <div style={{ position: "absolute", top: "50%", left: "35%", right: "30%", display: "flex", flexDirection: "column", alignItems: "center", gap: "1rem", width: "250px" }}>
              <span style={{ color: "#7E8299", fontSize: "1.2rem", fontWeight: "600" }}>Criando sua senha</span>
              <S.PasswordOverlay>
                {password ? '•'.repeat(password.length) : '••••••••'}
              </S.PasswordOverlay>
              <S.StrengthOverlay strengthLevel={passwordStrength.level}>
                <div style={{
                  width: "100%",
                  height: "4px",
                  backgroundColor: "#E7E8EC",
                  borderRadius: "2px",
                  overflow: "hidden",
                }}>
                  <div style={{
                    width: `${(passwordStrength.level / 5) * 100}%`,
                    height: "100%",
                    backgroundColor: passwordStrength.color,
                    transition: "all 0.3s ease"
                  }} />
                </div>
                {passwordStrength.text}
              </S.StrengthOverlay>
            </div>
          </S.SvgWrapper>
        </S.SvgContainer>
      </S.ContentColumn>
    </S.StepContainer>
  );
};

export default Step3;
