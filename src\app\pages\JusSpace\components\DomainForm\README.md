# Domain Form Component

A 3-step form component for domain registration within the JusSpace application.

## Overview

The DomainForm component provides a user-friendly interface for selecting and registering domains. It features a 3-step wizard with a 40/60 column layout, where the left column contains the form and the right column displays a browser mockup with the selected domain.

## Features

- **3-Step Wizard**: Domain selection, configuration, and finalization
- **Responsive Layout**: 40% form column, 60% preview column
- **Domain Selection**: 5 predefined domain options plus custom domain input
- **Real-time Preview**: Shows selected domain in browser mockup SVG
- **Form Validation**: Input sanitization and validation
- **Material-UI Integration**: Uses MUI components for consistent styling

## Components Structure

```
DomainForm/
├── index.js          # Main form container with step management
├── styles.js         # Styled components
├── Steps/
│   ├── Step1.js      # Domain selection step
│   ├── Step2.js      # Configuration step (placeholder)
│   └── Step3.js      # Finalization step
└── README.md         # This file
```

## Usage

### Basic Usage

```jsx
import { DomainForm } from './components/DomainForm';

function MyPage() {
  return <DomainForm />;
}
```

### Routing

The component is accessible at `/space/domain` route, which is configured in `BasePage.js`.

## Step Details

### Step 1: Domain Selection
- **Predefined Domains**: 5 chip-based options (advocacia, juridico, direito, advogados, escritorio)
- **Custom Domain**: Text input with validation (only letters, numbers, and hyphens)
- **Real-time Preview**: Shows selected domain in browser mockup

### Step 2: Professional Email
- **Email Creation**: Input field for creating professional email address
- **Domain Integration**: Uses domain selected in Step 1 as email domain
- **Input Validation**: Only allows letters, numbers, dots, hyphens, and underscores
- **Real-time Preview**: Shows complete email address as user types
- **Visual Mockup**: Uses email-form.png with email overlay in green field area

### Step 3: Finalization
- Shows summary of selected domain and professional email
- Confirmation interface with all selected options
- Final domain preview with confirmation badge
- Complete form data review before submission

## Styling

The component uses styled-components with the following design principles:
- Consistent with JusSpace color scheme (#01AB7D primary green)
- Responsive design with mobile breakpoints
- Material-UI component integration
- Clean, professional appearance

## Image Integration

The component uses different images for each step:

### Step 1: Domain Selection
- **Image**: `/media/jusspace/domain-form.svg` (browser mockup)
- **Domain Overlay**: Positioned at 4% from top, 21% margins, 6% height
- **Purpose**: Shows selected domain in browser address bar

### Step 2: Email Configuration
- **Image**: `/media/jusspace/email-form.png` (email interface mockup)
- **Email Overlay**: Positioned at 45% from top, 15% margins, 8% height
- **Purpose**: Shows professional email in green field area
- **Styling**: Semi-transparent green background with white text

### Responsive Design
- **Font sizes**: Adjust automatically for mobile devices
- **Positioning**: Maintains proportional overlay placement across screen sizes

## Form Data Management

The component manages form state internally and passes data between steps:

```javascript
const [formData, setFormData] = useState({
  selectedDomain: "",
  customDomain: "",
  professionalEmail: "",
  // Additional fields can be added for future steps
});
```

## Navigation

- **From JusSpace**: Click "Criar" button in JusDigital section
- **Direct Access**: Navigate to `/space/domain`
- **Step Navigation**: Previous/Next buttons within the form

## Future Enhancements

- Step 2: Add domain configuration options (email setup, DNS settings, etc.)
- API Integration: Connect to domain registration service
- Payment Integration: Add pricing and payment flow
- Email Notifications: Send confirmation emails
- Domain Availability Check: Real-time domain availability validation

## Dependencies

- React
- Material-UI (@mui/material)
- styled-components
- React Router (for navigation)

## Browser Support

The component supports all modern browsers and is responsive across desktop, tablet, and mobile devices.
